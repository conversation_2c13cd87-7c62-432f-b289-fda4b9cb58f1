/*
Copyright © 2025 wenchao <EMAIL>

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

	http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/
package cmd

import (
	"context"
	"os"
	"os/signal"
	"syscall"

	"github.com/spf13/cobra"

	"payment-backend/internal/app"
)

var cfgFile string

// serveCmd represents the serve command
var serveCmd = &cobra.Command{
	Use:   "serve",
	Short: "启动支付服务",
	Long:  `启动支付后端HTTP服务器`,
	Run: func(cmd *cobra.Command, args []string) {
		// 获取配置文件路径并设置到 app 包中
		configFile, _ := cmd.Flags().GetString("config")
		app.SetConfigPath(configFile)

		// 创建fx应用
		fxApp := app.NewApp()

		// 创建上下文用于优雅关闭
		ctx, cancel := context.WithCancel(context.Background())
		defer cancel()

		// 监听系统信号
		sigChan := make(chan os.Signal, 1)
		signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

		// 启动应用
		if err := fxApp.Start(ctx); err != nil {
			panic(err)
		}

		// 等待关闭信号
		<-sigChan

		// 优雅关闭
		if err := fxApp.Stop(ctx); err != nil {
			panic(err)
		}
	},
}

func init() {
	rootCmd.AddCommand(serveCmd)

	// Here you will define your flags and configuration settings.

	// Cobra supports Persistent Flags which will work for this command
	// and all subcommands, e.g.:
	// serveCmd.PersistentFlags().String("foo", "", "A help for foo")

	// Cobra supports local flags which will only run when this command
	// is called directly, e.g.:
	// serveCmd.Flags().BoolP("toggle", "t", false, "Help message for toggle")

	serveCmd.PersistentFlags().StringVar(&cfgFile, "config", "", "config file (default is ./configs/config.yaml)")

}
