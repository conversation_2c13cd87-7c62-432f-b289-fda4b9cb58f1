{"swagger": "2.0", "info": {"description": "支付后端服务API文档，提供订单管理、支付处理等功能", "title": "Payment Backend API", "termsOfService": "http://swagger.io/terms/", "contact": {"name": "wenchao", "url": "xxx", "email": "<EMAIL>"}, "license": {"name": "Apache 2.0", "url": "http://www.apache.org/licenses/LICENSE-2.0.html"}, "version": "1"}, "paths": {"/admin/orders": {"get": {"description": "----------------------------------------------------\n## 接口测试命令示例如下:\n\n### 设置服务器地址环境变量示例（可选，默认为 localhost:8080）\nexport SERVER_HOST=http://**************:8080 (Linux/macOS)\n$env:SERVER_HOST = \"http://localhost:8080\" (Windows PowerShell)\n\n### 管理员接口，获取所有订单列表，支持多种过滤条件和分页\n请求命令:\ncurl -X GET \"${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/admin/orders?limit=50&offset=0&pay_status=created&created_at_start=2025-07-01T00:00:00Z&created_at_end=2025-07-31T23:59:59Z\"\n\n返回示例：\n{\n\"orders\": [\n{\n\"id\": 7,\n\"order_id\": \"20250711160056STRIPE1943581257602433024\",\n\"user_id\": \"user123\",\n\"product_id\": \"prod_stripe_001\",\n\"product_desc\": \"Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq\",\n\"price_id\": \"price_1Rd4qlC53MAl6WmqQ9ORYbPq\",\n\"quantity\": 1,\n\"amount\": 0,\n\"net_amount\": 0,\n\"currency\": \"\",\n\"pay_status\": \"created\",\n\"payed_method\": \"\",\n\"psp_provider\": \"stripe\",\n\"card_number\": \"\",\n\"refund_status\": \"none\",\n\"psp_product_id\": \"\",\n\"psp_product_desc\": \"\",\n\"psp_price_id\": \"\",\n\"psp_payment_id\": \"\",\n\"psp_customer_id\": \"\",\n\"psp_customer_email\": \"\",\n\"psp_subscription_id\": \"\",\n\"created_at\": \"2025-07-11T16:00:56.463+08:00\",\n\"updated_at\": \"2025-07-11T16:00:56.463+08:00\",\n\"deleted\": false\n}\n],\n\"pagination\": {\n\"total\": 5,\n\"limit\": 50,\n\"offset\": 0,\n\"remaining\": 0\n}\n}\n\n\n### 更多 curl 测试命令请见项目文档\n----------------------------------------------------", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单管理"], "summary": "获取所有订单列表", "parameters": [{"maximum": 500, "minimum": 1, "type": "integer", "default": 50, "description": "每页数量，默认50，最大500", "name": "limit", "in": "query"}, {"minimum": 0, "type": "integer", "default": 0, "description": "偏移量，从0开始", "name": "offset", "in": "query"}, {"type": "string", "description": "用户ID过滤", "name": "user_id", "in": "query"}, {"type": "string", "description": "货币代码过滤，ISO-4217 币种, 如USD、CNY等", "name": "currency", "in": "query"}, {"type": "string", "description": "支付状态过滤，如 'created'(订单创建),'paid'(支付中),'succeeded'(支付成功),'failed'(支付失败),'expired'(支付过期),'cancelled'(取消支付) 等", "name": "pay_status", "in": "query"}, {"type": "string", "description": "支付方式过滤，待定", "name": "payed_method", "in": "query"}, {"type": "string", "description": "支付服务提供商过滤，如stripe、paypal 等", "name": "psp_provider", "in": "query"}, {"type": "string", "description": "支付时间开始，RFC3339格式，如2023-01-01T00:00:00Z", "name": "payed_at_start", "in": "query"}, {"type": "string", "description": "支付时间结束，RFC3339格式，如2023-12-31T23:59:59Z", "name": "payed_at_end", "in": "query"}, {"type": "string", "description": "退款状态过滤，如 'none'(未申请退款),'requested'(发起申请退款),'succeeded'(退款成功),'failed'(退款失败) 等", "name": "refund_status", "in": "query"}, {"type": "string", "description": "退款时间开始，RFC3339格式", "name": "refunded_at_start", "in": "query"}, {"type": "string", "description": "退款时间结束，RFC3339格式", "name": "refunded_at_end", "in": "query"}, {"type": "string", "description": "PSP价格ID过滤", "name": "psp_price_id", "in": "query"}, {"type": "string", "description": "PSP客户邮箱过滤", "name": "psp_customer_email", "in": "query"}, {"type": "string", "description": "PSP订阅ID过滤", "name": "psp_subscription_id", "in": "query"}], "responses": {"200": {"description": "成功返回订单列表", "schema": {"$ref": "#/definitions/domain.ListOrdersResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}}, "/api/v1/order-service/orders": {"post": {"description": "创建新的订单并生成支付链接，需要用户认证", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["订单管理"], "summary": "创建订单", "parameters": [{"type": "string", "example": "\"user123\"", "description": "用户ID", "name": "x-user-id", "in": "header", "required": true}, {"type": "string", "example": "\"customer\"", "description": "用户角色", "name": "x-role", "in": "header", "required": true}, {"description": "创建订单请求", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/domain.CreateOrderRequest"}}], "responses": {"303": {"description": "订单创建成功，返回支付链接", "schema": {"$ref": "#/definitions/domain.CreateOrderResponse"}}, "400": {"description": "请求参数错误, code=10001", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}, "500": {"description": "服务器内部错误, code=10003", "schema": {"$ref": "#/definitions/domain.ErrorResponse"}}}}}}, "definitions": {"domain.CreateOrderRequest": {"type": "object", "required": ["price_id", "product_id", "psp_provider", "quantity"], "properties": {"currency": {"type": "string", "example": "USD"}, "payed_method": {"type": "string", "example": "stripe"}, "price_id": {"type": "string", "example": "price_456"}, "product_desc": {"type": "string", "example": "Premium Subscription"}, "product_id": {"type": "string", "example": "prod_123"}, "psp_provider": {"type": "string", "example": "stripe"}, "quantity": {"type": "integer", "minimum": 1, "example": 1}}}, "domain.CreateOrderResponse": {"type": "object", "properties": {"amount": {"type": "number", "example": 99.99}, "checkout_url": {"type": "string", "example": "https://mock-payment.example.com/stripe/checkout/..."}, "currency": {"type": "string", "example": "USD"}, "expires_at": {"type": "string", "example": "2025-07-11T15:30:45Z"}, "order_id": {"type": "string", "example": "20250710153045999stripe1234567890123456789"}}}, "domain.ErrorResponse": {"type": "object", "properties": {"code": {"type": "integer"}, "message": {"type": "string", "example": "Missing required field: product_id"}}}, "domain.ListOrdersResponse": {"type": "object", "properties": {"orders": {"type": "array", "items": {"$ref": "#/definitions/domain.Order"}}, "pagination": {"$ref": "#/definitions/domain.PaginationResponse"}}}, "domain.Order": {"type": "object", "properties": {"amount": {"type": "number"}, "card_number": {"type": "string"}, "created_at": {"type": "string"}, "currency": {"type": "string"}, "deleted": {"type": "boolean"}, "deleted_at": {"type": "string"}, "id": {"type": "integer"}, "net_amount": {"type": "number"}, "order_id": {"type": "string"}, "pay_status": {"type": "string"}, "payed_at": {"type": "string"}, "payed_method": {"type": "string"}, "price_id": {"type": "string"}, "product_desc": {"type": "string"}, "product_id": {"type": "string"}, "psp_customer_email": {"type": "string"}, "psp_customer_id": {"type": "string"}, "psp_payment_id": {"type": "string"}, "psp_price_id": {"type": "string"}, "psp_product_desc": {"type": "string"}, "psp_product_id": {"type": "string"}, "psp_provider": {"type": "string"}, "psp_subscription_id": {"type": "string"}, "quantity": {"type": "integer"}, "refund_status": {"type": "string"}, "refunded_at": {"type": "string"}, "updated_at": {"type": "string"}, "user_id": {"type": "string"}}}, "domain.PaginationResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "offset": {"type": "integer"}, "remaining": {"type": "integer"}, "total": {"type": "integer"}}}}}