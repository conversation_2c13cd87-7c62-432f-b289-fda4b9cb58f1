definitions:
  domain.CreateOrderRequest:
    properties:
      currency:
        example: USD
        type: string
      payed_method:
        example: stripe
        type: string
      price_id:
        example: price_456
        type: string
      product_desc:
        example: Premium Subscription
        type: string
      product_id:
        example: prod_123
        type: string
      psp_provider:
        example: stripe
        type: string
      quantity:
        example: 1
        minimum: 1
        type: integer
    required:
    - price_id
    - product_id
    - psp_provider
    - quantity
    type: object
  domain.CreateOrderResponse:
    properties:
      amount:
        example: 99.99
        type: number
      checkout_url:
        example: https://mock-payment.example.com/stripe/checkout/...
        type: string
      currency:
        example: USD
        type: string
      expires_at:
        example: "2025-07-11T15:30:45Z"
        type: string
      order_id:
        example: 20250710153045999stripe1234567890123456789
        type: string
    type: object
  domain.ErrorResponse:
    properties:
      code:
        type: integer
      message:
        example: 'Missing required field: product_id'
        type: string
    type: object
  domain.ListOrdersResponse:
    properties:
      orders:
        items:
          $ref: '#/definitions/domain.Order'
        type: array
      pagination:
        $ref: '#/definitions/domain.PaginationResponse'
    type: object
  domain.Order:
    properties:
      amount:
        type: number
      card_number:
        type: string
      created_at:
        type: string
      currency:
        type: string
      deleted:
        type: boolean
      deleted_at:
        type: string
      id:
        type: integer
      net_amount:
        type: number
      order_id:
        type: string
      pay_status:
        type: string
      payed_at:
        type: string
      payed_method:
        type: string
      price_id:
        type: string
      product_desc:
        type: string
      product_id:
        type: string
      psp_customer_email:
        type: string
      psp_customer_id:
        type: string
      psp_payment_id:
        type: string
      psp_price_id:
        type: string
      psp_product_desc:
        type: string
      psp_product_id:
        type: string
      psp_provider:
        type: string
      psp_subscription_id:
        type: string
      quantity:
        type: integer
      refund_status:
        type: string
      refunded_at:
        type: string
      updated_at:
        type: string
      user_id:
        type: string
    type: object
  domain.PaginationResponse:
    properties:
      limit:
        type: integer
      offset:
        type: integer
      remaining:
        type: integer
      total:
        type: integer
    type: object
info:
  contact:
    email: <EMAIL>
    name: wenchao
    url: xxx
  description: 支付后端服务API文档，提供订单管理、支付处理等功能
  license:
    name: Apache 2.0
    url: http://www.apache.org/licenses/LICENSE-2.0.html
  termsOfService: http://swagger.io/terms/
  title: Payment Backend API
  version: "1"
paths:
  /admin/orders:
    get:
      consumes:
      - application/json
      description: |-
        ----------------------------------------------------
        ## 接口测试命令示例如下:

        ### 设置服务器地址环境变量示例（可选，默认为 localhost:8080）
        export SERVER_HOST=http://**************:8080 (Linux/macOS)
        $env:SERVER_HOST = "http://localhost:8080" (Windows PowerShell)

        ### 管理员接口，获取所有订单列表，支持多种过滤条件和分页
        请求命令:
        curl -X GET "${SERVER_HOST:-http://localhost:8080}/api/v1/order-service/admin/orders?limit=50&offset=0&pay_status=created&created_at_start=2025-07-01T00:00:00Z&created_at_end=2025-07-31T23:59:59Z"

        返回示例：
        {
        "orders": [
        {
        "id": 7,
        "order_id": "20250711160056STRIPE1943581257602433024",
        "user_id": "user123",
        "product_id": "prod_stripe_001",
        "product_desc": "Buy price_1Rd4qlC53MAl6WmqQ9ORYbPq",
        "price_id": "price_1Rd4qlC53MAl6WmqQ9ORYbPq",
        "quantity": 1,
        "amount": 0,
        "net_amount": 0,
        "currency": "",
        "pay_status": "created",
        "payed_method": "",
        "psp_provider": "stripe",
        "card_number": "",
        "refund_status": "none",
        "psp_product_id": "",
        "psp_product_desc": "",
        "psp_price_id": "",
        "psp_payment_id": "",
        "psp_customer_id": "",
        "psp_customer_email": "",
        "psp_subscription_id": "",
        "created_at": "2025-07-11T16:00:56.463+08:00",
        "updated_at": "2025-07-11T16:00:56.463+08:00",
        "deleted": false
        }
        ],
        "pagination": {
        "total": 5,
        "limit": 50,
        "offset": 0,
        "remaining": 0
        }
        }


        ### 更多 curl 测试命令请见项目文档
        ----------------------------------------------------
      parameters:
      - default: 50
        description: 每页数量，默认50，最大500
        in: query
        maximum: 500
        minimum: 1
        name: limit
        type: integer
      - default: 0
        description: 偏移量，从0开始
        in: query
        minimum: 0
        name: offset
        type: integer
      - description: 用户ID过滤
        in: query
        name: user_id
        type: string
      - description: 货币代码过滤，ISO-4217 币种, 如USD、CNY等
        in: query
        name: currency
        type: string
      - description: 支付状态过滤，如 'created'(订单创建),'paid'(支付中),'succeeded'(支付成功),'failed'(支付失败),'expired'(支付过期),'cancelled'(取消支付)
          等
        in: query
        name: pay_status
        type: string
      - description: 支付方式过滤，待定
        in: query
        name: payed_method
        type: string
      - description: 支付服务提供商过滤，如stripe、paypal 等
        in: query
        name: psp_provider
        type: string
      - description: 支付时间开始，RFC3339格式，如2023-01-01T00:00:00Z
        in: query
        name: payed_at_start
        type: string
      - description: 支付时间结束，RFC3339格式，如2023-12-31T23:59:59Z
        in: query
        name: payed_at_end
        type: string
      - description: 退款状态过滤，如 'none'(未申请退款),'requested'(发起申请退款),'succeeded'(退款成功),'failed'(退款失败)
          等
        in: query
        name: refund_status
        type: string
      - description: 退款时间开始，RFC3339格式
        in: query
        name: refunded_at_start
        type: string
      - description: 退款时间结束，RFC3339格式
        in: query
        name: refunded_at_end
        type: string
      - description: PSP价格ID过滤
        in: query
        name: psp_price_id
        type: string
      - description: PSP客户邮箱过滤
        in: query
        name: psp_customer_email
        type: string
      - description: PSP订阅ID过滤
        in: query
        name: psp_subscription_id
        type: string
      produces:
      - application/json
      responses:
        "200":
          description: 成功返回订单列表
          schema:
            $ref: '#/definitions/domain.ListOrdersResponse'
        "400":
          description: 请求参数错误, code=10001
          schema:
            $ref: '#/definitions/domain.ErrorResponse'
        "500":
          description: 服务器内部错误, code=10003
          schema:
            $ref: '#/definitions/domain.ErrorResponse'
      summary: 获取所有订单列表
      tags:
      - 订单管理
  /api/v1/order-service/orders:
    post:
      consumes:
      - application/json
      description: 创建新的订单并生成支付链接，需要用户认证
      parameters:
      - description: 用户ID
        example: '"user123"'
        in: header
        name: x-user-id
        required: true
        type: string
      - description: 用户角色
        example: '"customer"'
        in: header
        name: x-role
        required: true
        type: string
      - description: 创建订单请求
        in: body
        name: request
        required: true
        schema:
          $ref: '#/definitions/domain.CreateOrderRequest'
      produces:
      - application/json
      responses:
        "303":
          description: 订单创建成功，返回支付链接
          schema:
            $ref: '#/definitions/domain.CreateOrderResponse'
        "400":
          description: 请求参数错误, code=10001
          schema:
            $ref: '#/definitions/domain.ErrorResponse'
        "500":
          description: 服务器内部错误, code=10003
          schema:
            $ref: '#/definitions/domain.ErrorResponse'
      summary: 创建订单
      tags:
      - 订单管理
swagger: "2.0"
