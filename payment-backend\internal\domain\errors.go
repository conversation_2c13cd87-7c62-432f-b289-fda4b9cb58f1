package domain

// 常用错误代码, int32
const (
	ErrCodePaymentNotFound    = 10000
	ErrCodeInvalidRequest     = 10001
	ErrCodeInvalidProvider    = 10002
	ErrCodeCreateOrderFailed  = 10003
	ErrCodeGatewayUnavailable = 10004
	ErrCodeRefundFailed       = 10005
	ErrCodeInternalError      = 10006
)

const (
	MsgUserContext    = "Invalid user context."
	MsgInvalidRequest = "Invalid request."
)

// ErrorResponse 通用错误响应
type ErrorResponse struct {
	Code    int    `json:"code,omitempty" comment:"错误代码"`
	Message string `json:"message,omitempty" example:"Missing required field: product_id" comment:"详细错误信息"`
}

// NewErrorResponse 创建通用错误
func NewErrorResponse(code int, message string) *ErrorResponse {
	return &ErrorResponse{
		Code:    code,
		Message: message,
	}
}
