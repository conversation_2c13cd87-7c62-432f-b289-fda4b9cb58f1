package logger

import (
	"os"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"

	"payment-backend/internal/config"
)

// Logger 日志接口
type Logger interface {
	Debug(msg string, fields ...zap.Field)
	Info(msg string, fields ...zap.Field)
	Warn(msg string, fields ...zap.Field)
	Error(msg string, fields ...zap.Field)
	Fatal(msg string, fields ...zap.Field)
	With(fields ...zap.Field) Logger
	Sync() error
	GetZapLogger() *zap.Logger
}

// zapLogger zap日志实现
type zapLogger struct {
	logger *zap.Logger
}

func (l *zapLogger) GetZapLogger() *zap.Logger {
	return l.logger
}

// NewLogger 创建新的日志实例
func NewLogger(cfg *config.LogConfig) (Logger, error) {
	// 配置编码器
	var encoderConfig zapcore.EncoderConfig
	if cfg.Format == "json" {
		encoderConfig = zap.NewProductionEncoderConfig()
	} else {
		encoderConfig = zap.NewDevelopmentEncoderConfig()
		encoderConfig.EncodeLevel = zapcore.CapitalColorLevelEncoder
	}

	encoderConfig.TimeKey = "timestamp"
	encoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder

	// 创建编码器
	var encoder zapcore.Encoder
	if cfg.Format == "json" {
		encoder = zapcore.NewJSONEncoder(encoderConfig)
	} else {
		encoder = zapcore.NewConsoleEncoder(encoderConfig)
	}

	// 配置日志级别
	level, err := zapcore.ParseLevel(cfg.Level)
	if err != nil {
		level = zapcore.InfoLevel
	}

	// 多输出支持
	var writeSyncers []zapcore.WriteSyncer

	// 配置输出
	if cfg.Output == "file" && cfg.Filename != "" {
		file, err := os.OpenFile(cfg.Filename, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0666)
		if err != nil {
			return nil, err
		}
		writeSyncers = append(writeSyncers, zapcore.AddSync(file))
	} else {
		writeSyncers = append(writeSyncers, zapcore.AddSync(os.Stdout))
		writeSyncers = append(writeSyncers, zapcore.AddSync(os.Stderr))
	}

	multiWriter := zapcore.NewMultiWriteSyncer(writeSyncers...)

	// 创建核心
	core := zapcore.NewCore(encoder, multiWriter, level)

	// 创建logger
	logger := zap.New(core, zap.AddCaller(), zap.AddStacktrace(zapcore.ErrorLevel))

	return &zapLogger{logger: logger}, nil
}

// Debug 调试日志
func (l *zapLogger) Debug(msg string, fields ...zap.Field) {
	l.logger.Debug(msg, fields...)
}

// Info 信息日志
func (l *zapLogger) Info(msg string, fields ...zap.Field) {
	l.logger.Info(msg, fields...)
}

// Warn 警告日志
func (l *zapLogger) Warn(msg string, fields ...zap.Field) {
	l.logger.Warn(msg, fields...)
}

// Error 错误日志
func (l *zapLogger) Error(msg string, fields ...zap.Field) {
	l.logger.Error(msg, fields...)
}

// Fatal 致命错误日志
func (l *zapLogger) Fatal(msg string, fields ...zap.Field) {
	l.logger.Fatal(msg, fields...)
}

// With 添加字段
func (l *zapLogger) With(fields ...zap.Field) Logger {
	return &zapLogger{logger: l.logger.With(fields...)}
}

// Sync 同步日志
func (l *zapLogger) Sync() error {
	return l.logger.Sync()
}

// NewDevelopmentLogger 创建开发环境日志
func NewDevelopmentLogger() (Logger, error) {
	logger, err := zap.NewDevelopment()
	if err != nil {
		return nil, err
	}
	return &zapLogger{logger: logger}, nil
}

// NewProductionLogger 创建生产环境日志
func NewProductionLogger() (Logger, error) {
	logger, err := zap.NewProduction()
	if err != nil {
		return nil, err
	}
	return &zapLogger{logger: logger}, nil
}

// Field 日志字段
type Field = zap.Field

// Object 创建对象字段
func Object(key string, value interface{}) Field {
	return zap.Any(key, value)
}

// String 创建字符串字段
func String(key, value string) Field {
	return zap.String(key, value)
}

// Int64 创建int64字段
func Int64(key string, value int64) Field {
	return zap.Int64(key, value)
}

// Int 创建int字段
func Int(key string, value int) Field {
	return zap.Int(key, value)
}

// Error 创建错误字段
func Error(err error) Field {
	return zap.Error(err)
}
