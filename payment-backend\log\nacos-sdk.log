2025-07-15T15:18:03.439+0800	ERROR	cache/disk_cache.go:86	failed to delete config file,cache:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@dev1 ,value: ,err:remove C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@dev1: The system cannot find the file specified.
2025-07-15T16:01:05.269+0800	ERROR	cache/disk_cache.go:86	failed to delete config file,cache:C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@dev1 ,value: ,err:remove C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\cache\config\payment-backend.yaml@@DEFAULT_GROUP@@dev1: The system cannot find the file specified.
2025-07-15T16:01:52.799+0800	ERROR	rpc/rpc_client.go:423	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = received context error while waiting for new LB policy update: context deadline exceeded
2025-07-15T16:13:01.098+0800	ERROR	rpc/rpc_client.go:423	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = received context error while waiting for new LB policy update: context deadline exceeded
2025-07-15T17:27:34.866+0800	INFO	nacos_client/nacos_client.go:65	logDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\log> cacheDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\cache>
2025-07-15T17:27:35.099+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 54972
2025-07-15T17:27:35.100+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] 050f316a-8ec6-4a5f-bd58-109c300e39be try to connect to server on start up, server: {serverIp:************* serverPort:8848 serverGrpcPort:9848}
2025-07-15T17:27:35.456+0800	INFO	rpc/rpc_client.go:234	050f316a-8ec6-4a5f-bd58-109c300e39be success to connect to server {serverIp:************* serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752571655190_10.188.29.7_54007
2025-07-15T17:27:35.457+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-a7079f7c-fc60-4bfb-97b0-00f4274589f9 try to connect to server on start up, server: {serverIp:************* serverPort:8848 serverGrpcPort:9848}
2025-07-15T17:27:35.853+0800	INFO	rpc/rpc_client.go:234	config-0-a7079f7c-fc60-4bfb-97b0-00f4274589f9 success to connect to server {serverIp:************* serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752571655508_10.188.29.7_54009
2025-07-15T17:27:35.919+0800	INFO	naming_grpc/naming_grpc_proxy.go:95	register instance namespaceId:<dev1>,serviceName:<payment-service> with instance:<{"instanceId":"","ip":"0.0.0.0","port":20000,"weight":1,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"","serviceName":"","metadata":{"dubbo.endpoints":"[{\"port\":20000,\"protocol\":\"tri\"}]","dubbo.metadata-service.url-params":"{\"group\":\"payment-service\",\"port\":\"54005\",\"protocol\":\"dubbo\",\"release\":\"3.2.0\",\"serialization\":\"hessian2\",\"version\":\"1.0.0\"}","dubbo.metadata.revision":"**********","dubbo.metadata.storage-type":"local","dubbo.subscribed-services.revision":"0","id":"","meta-v":"2.0.0"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}>
2025-07-16T10:16:31.032+0800	ERROR	rpc/rpc_client.go:423	client sendHealthCheck failed,err=rpc error: code = DeadlineExceeded desc = received context error while waiting for new LB policy update: context deadline exceeded
2025-07-16T10:17:11.612+0800	INFO	nacos_client/nacos_client.go:65	logDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\log> cacheDir:<C:\file\z\iscas\gitlab\aibook\platform\payment-service\payment-backend\cache>
2025-07-16T10:17:12.121+0800	INFO	naming_http/push_receiver.go:89	udp server start, port: 55932
2025-07-16T10:17:12.122+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] dd8ed1e3-a171-459b-b7ca-eb4f5d0fa6b7 try to connect to server on start up, server: {serverIp:************* serverPort:8848 serverGrpcPort:9848}
2025-07-16T10:17:13.368+0800	INFO	rpc/rpc_client.go:234	dd8ed1e3-a171-459b-b7ca-eb4f5d0fa6b7 success to connect to server {serverIp:************* serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752632232491_10.188.29.7_61964
2025-07-16T10:17:13.368+0800	INFO	rpc/rpc_client.go:224	[RpcClient.Start] config-0-fa1bfecf-6968-4b81-a189-c67adb364c19 try to connect to server on start up, server: {serverIp:************* serverPort:8848 serverGrpcPort:9848}
2025-07-16T10:17:14.572+0800	INFO	rpc/rpc_client.go:234	config-0-fa1bfecf-6968-4b81-a189-c67adb364c19 success to connect to server {serverIp:************* serverPort:8848 serverGrpcPort:9848} on start up, connectionId=1752632233697_10.188.29.7_61966
2025-07-16T10:17:15.006+0800	INFO	naming_grpc/naming_grpc_proxy.go:95	register instance namespaceId:<dev1>,serviceName:<payment-service> with instance:<{"instanceId":"","ip":"0.0.0.0","port":20000,"weight":1,"healthy":true,"enabled":true,"ephemeral":true,"clusterName":"","serviceName":"","metadata":{"dubbo.endpoints":"[{\"port\":20000,\"protocol\":\"tri\"}]","dubbo.metadata-service.url-params":"{\"group\":\"payment-service\",\"port\":\"61961\",\"protocol\":\"dubbo\",\"release\":\"3.2.0\",\"serialization\":\"hessian2\",\"version\":\"1.0.0\"}","dubbo.metadata.revision":"**********","dubbo.metadata.storage-type":"local","dubbo.subscribed-services.revision":"0","id":"","meta-v":"2.0.0"},"instanceHeartBeatInterval":0,"ipDeleteTimeout":0,"instanceHeartBeatTimeOut":0}>
