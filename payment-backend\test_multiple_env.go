package main

import (
	"fmt"
	"os"
	"strings"

	"payment-backend/internal/config"
)

func main() {
	// 设置多个环境变量
	os.Setenv("PAYMENT_NACOS_NAMESPACE", "dev1")
	os.Setenv("PAYMENT_NACOS_USERNAME", "env-user")
	os.Setenv("PAYMENT_NACOS_PASSWORD", "env-password")
	os.Setenv("PAYMENT_NACOS_CONFIG_DATA_ID", "env-config.yaml")
	os.Setenv("PAYMENT_NACOS_CONFIG_GROUP", "ENV_GROUP")
	os.Setenv("PAYMENT_NACOS_CONFIG_TIMEOUT", "60")

	// 测试环境变量读取
	fmt.Printf("Environment variables:\n")
	fmt.Printf("  PAYMENT_NACOS_NAMESPACE: %s\n", os.<PERSON>env("PAYMENT_NACOS_NAMESPACE"))
	fmt.Printf("  PAYMENT_NACOS_USERNAME: %s\n", os.Getenv("PAYMENT_NACOS_USERNAME"))
	fmt.Printf("  PAYMENT_NACOS_PASSWORD: %s\n", os.Getenv("PAYMENT_NACOS_PASSWORD"))
	fmt.Printf("  PAYMENT_NACOS_CONFIG_DATA_ID: %s\n", os.Getenv("PAYMENT_NACOS_CONFIG_DATA_ID"))
	fmt.Printf("  PAYMENT_NACOS_CONFIG_GROUP: %s\n", os.Getenv("PAYMENT_NACOS_CONFIG_GROUP"))
	fmt.Printf("  PAYMENT_NACOS_CONFIG_TIMEOUT: %s\n", os.Getenv("PAYMENT_NACOS_CONFIG_TIMEOUT"))

	fmt.Println("\n" + strings.Repeat("=", 50))

	// 加载配置
	cfg, err := config.LoadConfig("./configs/config.yaml")
	if err != nil {
		fmt.Printf("Error loading config: %v\n", err)
		return
	}

	fmt.Println("\n" + strings.Repeat("=", 50))

	// 输出最终的Nacos配置
	fmt.Printf("Final Nacos config (should reflect environment variables):\n")
	fmt.Printf("  Enabled: %v\n", cfg.Nacos.Enabled)
	fmt.Printf("  Namespace: %s (expected: dev1)\n", cfg.Nacos.Namespace)
	fmt.Printf("  Username: %s (expected: env-user)\n", cfg.Nacos.Username)
	fmt.Printf("  Password: %s (expected: env-password)\n", cfg.Nacos.Password)
	fmt.Printf("  Endpoints: %v\n", cfg.Nacos.Endpoints)
	fmt.Printf("  Config.Enabled: %v\n", cfg.Nacos.Config.Enabled)
	fmt.Printf("  Config.DataID: %s (expected: env-config.yaml)\n", cfg.Nacos.Config.DataID)
	fmt.Printf("  Config.Group: %s (expected: ENV_GROUP)\n", cfg.Nacos.Config.Group)
	fmt.Printf("  Config.Timeout: %d (expected: 60)\n", cfg.Nacos.Config.Timeout)

	// 验证结果
	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Printf("Verification:\n")

	success := true
	if cfg.Nacos.Namespace != "dev1" {
		fmt.Printf("❌ Namespace override failed: got %s, expected dev1\n", cfg.Nacos.Namespace)
		success = false
	} else {
		fmt.Printf("✅ Namespace override successful\n")
	}

	if cfg.Nacos.Username != "env-user" {
		fmt.Printf("❌ Username override failed: got %s, expected env-user\n", cfg.Nacos.Username)
		success = false
	} else {
		fmt.Printf("✅ Username override successful\n")
	}

	if cfg.Nacos.Password != "env-password" {
		fmt.Printf("❌ Password override failed: got %s, expected env-password\n", cfg.Nacos.Password)
		success = false
	} else {
		fmt.Printf("✅ Password override successful\n")
	}

	if cfg.Nacos.Config.DataID != "env-config.yaml" {
		fmt.Printf("❌ DataID override failed: got %s, expected env-config.yaml\n", cfg.Nacos.Config.DataID)
		success = false
	} else {
		fmt.Printf("✅ DataID override successful\n")
	}

	if cfg.Nacos.Config.Group != "ENV_GROUP" {
		fmt.Printf("❌ Group override failed: got %s, expected ENV_GROUP\n", cfg.Nacos.Config.Group)
		success = false
	} else {
		fmt.Printf("✅ Group override successful\n")
	}

	if cfg.Nacos.Config.Timeout != 60 {
		fmt.Printf("❌ Timeout override failed: got %d, expected 60\n", cfg.Nacos.Config.Timeout)
		success = false
	} else {
		fmt.Printf("✅ Timeout override successful\n")
	}

	if success {
		fmt.Printf("\n🎉 All environment variable overrides are working correctly!\n")
	} else {
		fmt.Printf("\n❌ Some environment variable overrides failed.\n")
	}
}
